---
import '@fontsource-variable/inter';
import '@fontsource-variable/faustina';
import '@fontsource-variable/karla';

// 'DM Sans'
// Nunito
// Dosis
// Outfit
// Roboto
// Literata
// 'IBM Plex Sans'
// Karla
// Poppins
// '<PERSON>ra Sans'
// 'Li<PERSON> Franklin'
// Inconsolata
// Raleway
// <PERSON>
// 'Space Grotesk'
// Urbanist
---

<style is:inline>
  :root {
    --aw-font-sans: 'Inter Variable';
    --aw-font-serif: 'Faustina Variable';
    --aw-font-heading: 'Karla Variable';

    --color-primary: 233 241 242;
    --color-secondary: 133 160 166;
    --color-accent: 166 137 111;
    --color-muted: 208 216 217;
    --color-dark: 57 57 64;

    --aw-color-primary: #e9f1f2;
    --aw-color-secondary: #85a0a6;
    --aw-color-accent: #a6896f;

    --aw-color-text-heading: rgb(0 0 0);
    --aw-color-text-default: rgb(16 16 16);
    --aw-color-text-muted: rgb(16 16 16 / 66%);
    --aw-color-bg-page: rgb(255 255 255);

    --aw-color-bg-page-dark: rgb(3 6 32);

    ::selection {
      background-color: lavender;
    }
  }

  .dark {
    --aw-font-sans: 'Inter Variable';
    --aw-font-serif: 'Inter Variable';
    --aw-font-heading: 'Inter Variable';

    --aw-color-primary: rgb(1 97 239);
    --aw-color-secondary: rgb(1 84 207);
    --aw-color-accent: rgb(109 40 217);

    --aw-color-text-heading: rgb(247, 248, 248);
    --aw-color-text-default: rgb(229 236 246);
    --aw-color-text-muted: rgb(229 236 246 / 66%);
    --aw-color-bg-page: rgb(3 6 32);

    ::selection {
      background-color: black;
      color: snow;
    }
  }
</style>
