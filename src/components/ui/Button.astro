---
import { Icon } from 'astro-icon/components';
import { twMerge } from 'tailwind-merge';
import type { CallToAction as Props } from '~/types';

const {
  variant = 'secondary',
  target,
  text = Astro.slots.render('default'),
  icon = '',
  class: className = '',
  type,
  ...rest
} = Astro.props;

const variants = {
  primary: 'btn-primary',
  secondary: 'btn-secondary',
  tertiary: 'btn btn-tertiary',
  outline: 'btn-outline',
  link: 'cursor-pointer hover:text-primary',
};
---

{
  type === 'button' || type === 'submit' || type === 'reset' ? (
    <button type={type} class={twMerge(variants[variant] || '', className)} {...rest}>
      <Fragment set:html={text} />
      {icon && <Icon name={icon} class="w-5 h-5 ml-1 -mr-1.5 rtl:mr-1 rtl:-ml-1.5 inline-block" />}
    </button>
  ) : (
    <a
      class={twMerge(variants[variant] || '', className)}
      {...(target ? { target: target, rel: 'noopener noreferrer' } : {})}
      {...rest}
    >
      <Fragment set:html={text} />
      {icon && <Icon name={icon} class="w-5 h-5 ml-1 -mr-1.5 rtl:mr-1 rtl:-ml-1.5 inline-block" />}
    </a>
  )
}
