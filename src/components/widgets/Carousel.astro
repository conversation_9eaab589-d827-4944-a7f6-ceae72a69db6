---
import type { Image } from '~/types';

export interface Props {
  id?: string;
  slides?: Array<{
    title?: string;
    subtitle?: string;
    image?: Image;
    content?: string;
  }>;
  showPagination?: boolean;
  showNavigation?: boolean;
  autoplay?: boolean;
  loop?: boolean;
  centeredSlides?: boolean;
  spaceBetween?: number;
  slidesPerView?: number | 'auto';
  class?: string;
}

const {
  id = 'carousel',
  slides = [
    { title: 'Slide 1', content: 'Sample content for slide 1' },
    { title: 'Slide 2', content: 'Sample content for slide 2' },
    { title: 'Slide 3', content: 'Sample content for slide 3' },
    { title: 'Slide 4', content: 'Sample content for slide 4' },
    { title: 'Slide 5', content: 'Sample content for slide 5' },
    { title: 'Slide 6', content: 'Sample content for slide 6' },
  ],
  showPagination = true,
  showNavigation = true,
  autoplay = false,
  loop = true,
  centeredSlides = true,
  spaceBetween = 30,
  slidesPerView = 4,
  class: className = '',
} = Astro.props;

const uniqueId = `${id}-${Math.random().toString(36).substring(2, 9)}`;
---

<div class:list={['w-full relative', className]}>
  <div
    class={`swiper ${uniqueId} swiper-container relative`}
    data-autoplay={autoplay}
    data-loop={loop}
    data-centered-slides={centeredSlides}
    data-space-between={spaceBetween}
    data-slides-per-view={slidesPerView}
  >
    <div class="swiper-wrapper">
      {
        slides.map((slide, index) => (
          <div class="swiper-slide">
            <div class="bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl h-96 flex flex-col justify-center items-center p-6 text-center">
              {slide.image && (
                <img
                  src={slide.image.src}
                  alt={slide.image.alt || slide.title || `Slide ${index + 1}`}
                  class="w-full h-48 object-cover rounded-lg mb-4"
                />
              )}
              {slide.title && <h3 class="text-2xl font-semibold text-primary mb-2">{slide.title}</h3>}
              {slide.subtitle && <p class="text-lg text-secondary mb-3">{slide.subtitle}</p>}
              {slide.content && <p class="text-muted">{slide.content}</p>}
            </div>
          </div>
        ))
      }
    </div>

    {showPagination && <div class="swiper-pagination" />}

    {
      showNavigation && (
        <>
          <div class="swiper-button-next" />
          <div class="swiper-button-prev" />
        </>
      )
    }
  </div>
</div>

<style>
  .swiper-wrapper {
    width: 100%;
    height: max-content !important;
    padding-bottom: 64px !important;
    -webkit-transition-timing-function: linear !important;
    transition-timing-function: linear !important;
    position: relative;
  }
  .swiper-pagination-bullet {
    background: #4f46e5;
  }
  .swiper-pagination-bullet-active {
    background: #4f46e5 !important;
  }
</style>

<script>
  import { Swiper } from 'swiper';
  import type { SwiperOptions } from 'swiper/types';
  import 'swiper/css';
  import 'swiper/css/pagination';
  import 'swiper/css/navigation';

  document.addEventListener('DOMContentLoaded', function () {
    // Get all carousel elements
    const carousels = document.querySelectorAll('.swiper') as NodeListOf<HTMLElement>;

    carousels.forEach((carousel: HTMLElement) => {
      // Get the carousel's data attributes or use defaults
      const autoplay = carousel.dataset.autoplay === 'true';
      const loop = carousel.dataset.loop !== 'false';
      const centeredSlides = carousel.dataset.centeredSlides !== 'false';
      const spaceBetween = parseInt(carousel.dataset.spaceBetween || '30') || 30;
      const slidesPerView = carousel.dataset.slidesPerView || 'auto';
      const showPagination = carousel.querySelector('.swiper-pagination') !== null;
      const showNavigation = carousel.querySelector('.swiper-button-next') !== null;

      const swiperConfig: Partial<SwiperOptions> = {
        centeredSlides,
        loop,
        spaceBetween,
        slidesPerView: slidesPerView === 'auto' ? 'auto' : parseInt(slidesPerView),
        slideToClickedSlide: true,
        breakpoints: {
          1920: {
            slidesPerView: slidesPerView === 'auto' ? 'auto' : Math.min(4, parseInt(slidesPerView)),
            spaceBetween: spaceBetween,
          },
          1028: {
            slidesPerView: slidesPerView === 'auto' ? 'auto' : Math.min(2, parseInt(slidesPerView)),
            spaceBetween: Math.max(10, spaceBetween / 3),
          },
          990: {
            slidesPerView: 1,
            spaceBetween: 0,
          },
        },
      };

      // Add pagination if element exists
      if (showPagination) {
        swiperConfig.pagination = {
          el: carousel.querySelector('.swiper-pagination') as HTMLElement,
          clickable: true,
        };
      }

      // Add navigation if elements exist
      if (showNavigation) {
        swiperConfig.navigation = {
          nextEl: carousel.querySelector('.swiper-button-next') as HTMLElement,
          prevEl: carousel.querySelector('.swiper-button-prev') as HTMLElement,
        };
      }

      // Add autoplay if enabled
      if (autoplay) {
        swiperConfig.autoplay = {
          delay: 3000,
          disableOnInteraction: false,
        };
      }

      // Initialize Swiper
      new Swiper(carousel, swiperConfig);
    });
  });
</script>
