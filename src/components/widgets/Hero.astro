---
import Image from '~/components/common/Image.astro';
import Button from '~/components/ui/Button.astro';

import type { Hero as Props } from '~/types';

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline = await Astro.slots.render('tagline'),

  content = await Astro.slots.render('content'),
  actions = await Astro.slots.render('actions'),
  image = await Astro.slots.render('image'),

  class: extraClass,
  id,
  bg = await Astro.slots.render('bg'),
} = Astro.props;
---

<section class:list={['relative h-screen md:-mt-[80px] not-prose', extraClass]} {...id ? { id } : {}}>
  <div class="absolute inset-0 pointer-events-none overflow-hidden" aria-hidden="true">
    <slot name="bg">
      {bg ? <Fragment set:html={bg} /> : null}
    </slot>
  </div>
  <div class="relative h-full max-w-7xl mx-auto px-4 sm:px-6">
    <div class="pt-0 md:pt-[80px] pointer-events-none"></div>
    <div class="w-full flex flex-col justify-center py-12 md:py-44">
      <div class="pb-10 md:pb-16 text-white">
        {
          tagline && (
            <p
              class="text-base text-secondary dark:text-blue-200 font-bold tracking-wide uppercase intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
              set:html={tagline}
            />
          )
        }
        {
          title && (
            <h1
              class="text-5xl md:text-6xl font-bold leading-tighter tracking-tighter mb-4 font-heading dark:text-gray-200 intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
              set:html={title}
            />
          )
        }
        <div class="max-w-3xl">
          {
            subtitle && (
              <p
                class="text-xl mb-6 dark:text-slate-300 intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
                set:html={subtitle}
              />
            )
          }
          {
            actions && (
              <div class="max-w-xs sm:max-w-md flex flex-nowrap flex-col justify-center sm:flex-row sm:justify-start gap-4 intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade">
                {Array.isArray(actions) ? (
                  actions.map((action) => (
                    <div class="flex w-full sm:w-auto">
                      <Button {...(action || {})} class="w-full sm:mb-0" />
                    </div>
                  ))
                ) : (
                  <Fragment set:html={actions} />
                )}
              </div>
            )
          }
        </div>
        {content && <Fragment set:html={content} />}
      </div>
      <div
        class="intersect-once intercept-no-queue intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
      >
        {
          image && (
            <div class="relative m-auto max-w-5xl">
              {typeof image === 'string' ? (
                <Fragment set:html={image} />
              ) : (
                <Image
                  class="mx-auto rounded-md w-full"
                  widths={[400, 768, 1024, 2040]}
                  sizes="(max-width: 767px) 400px, (max-width: 1023px) 768px, (max-width: 2039px) 1024px, 2040px"
                  loading="eager"
                  width={1024}
                  height={576}
                  {...image}
                />
              )}
            </div>
          )
        }
      </div>
    </div>
  </div>
</section>
