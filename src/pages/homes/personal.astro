---
import Layout from '~/layouts/PageLayout.astro';

import Header from '~/components/widgets/Header.astro';
import Hero from '~/components/widgets/Hero.astro';
import Content from '~/components/widgets/Content.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import Features3 from '~/components/widgets/Features3.astro';
import Testimonials from '~/components/widgets/Testimonials.astro';
import Steps from '~/components/widgets/Steps.astro';
import BlogLatestPosts from '~/components/widgets/BlogLatestPosts.astro';
import { getPermalink } from '~/utils/permalinks';

const metadata = {
  title: 'Personal Homepage Demo',
};
---

<Layout metadata={metadata}>
  <Fragment slot="announcement"></Fragment>
  <Fragment slot="header">
    <Header
      links={[
        { text: 'Home', href: '#' },
        { text: 'About', href: '#about' },
        { text: 'Resume', href: '#resume' },
        { text: 'Porfolio', href: '#porfolio' },
        { text: 'Blog', href: '#blog' },
        { text: 'Github', href: 'https://github.com/onwidget' },
      ]}
      actions={[
        {
          text: 'Hire me',
          href: '#',
        },
      ]}
      isSticky
      showToggleTheme
    />
  </Fragment>

  <!-- Hero2 Widget ******************* -->

  <Hero
    id="hero"
    title="Sarah Johnson"
    tagline="Personal Web Demo"
    actions={[{ variant: 'primary', text: 'Hire me', href: getPermalink('/contact#form') }]}
  >
    <Fragment slot="subtitle">
      I'm a Graphic Designer passionate about crafting visual stories. <br /> With 5 years of experience and a degree from
      New York University's School of Design. I infuse vitality into brands and designs, transforming concepts into captivating
      realities.
    </Fragment>
  </Hero>

  <!-- Content Widget **************** -->

  <Content
    id="about"
    columns={3}
    items={[
      {
        icon: 'tabler:brand-dribbble',
        callToAction: {
          target: '_blank',
          text: 'Dribbble',
          href: '#',
        },
      },
      {
        icon: 'tabler:brand-behance',
        callToAction: {
          target: '_blank',
          text: 'Behance',
          href: '#',
        },
      },
      {
        icon: 'tabler:brand-pinterest',
        callToAction: {
          target: '_blank',
          text: 'Pinterest',
          href: '#',
        },
      },
    ]}
    image={{
      src: 'https://images.unsplash.com/photo-1491349174775-aaafddd81942?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80',
      alt: 'Colorful Image',
      loading: 'eager',
    }}
  >
    <Fragment slot="content">
      <h2 class="text-2xl font-bold tracking-tight dark:text-white sm:text-3xl mb-2">About me</h2>
      <p>
        Welcome to my creative journey. My work is a testament to my commitment to bringing ideas to life, where each
        pixel becomes a brushstroke in the canvas of imagination.
      </p>
      <br />
      <p>
        I find inspiration in the world around me, whether through the pages of a captivating novel, the intricate
        details of typography, or the vibrant hues of nature during my outdoor escapades.
      </p>
      <br />
      <p>If you're curious to dive deeper into my work, you can follow me:</p>
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Content>

  <!-- Steps Widget ****************** -->

  <Steps
    id="resume"
    title="Work experience"
    items={[
      {
        title:
          'Graphic Designer <br /> <span class="font-normal">ABC Design Studio, New York, NY</span> <br /> <span class="text-sm font-normal">2021 - Present</span>',
        description: `Collaborate with clients to understand design requirements and objectives. <br /> Develop branding solutions, including logos, color palettes, and brand guidelines. <br /> Design marketing materials such as brochures, posters, and digital assets. <br /> Create visually appealing user interfaces for websites and applications.`,
        icon: 'tabler:briefcase',
      },
      {
        title:
          'Junior Graphic Designer <br /> <span class="font-normal">XYZ Creative Agency, Los Angeles, CA</span> <br /> <span class="text-sm font-normal">2018 - 2021</span>',
        description: `Assisted senior designers in creating design concepts and visual assets. <br /> Contributed to the development of brand identities and marketing collateral. <br /> Collaborated with the marketing team to ensure consistent design across campaigns. <br /> Gained hands-on experience in various design software and tools.`,
        icon: 'tabler:briefcase',
      },
    ]}
    classes={{ container: 'max-w-3xl' }}
  />

  <!-- Steps Widget ****************** -->

  <Steps
    id="resume"
    title="Education"
    items={[
      {
        title: `Master of Fine Arts in Graphic Design <br /> <span class="font-normal">New York University's School of Design</span> <br /> <span class="text-sm font-normal">2018 - 2020</span>`,
        icon: 'tabler:school',
      },
      {
        title: `Bachelor of Arts in Graphic Design <br /> <span class="font-normal">New York University's School of Design</span> <br /> <span class="text-sm font-normal">2014 - 2018</span>`,
        icon: 'tabler:school',
      },
    ]}
    classes={{ container: 'max-w-3xl' }}
  />

  <!-- Features3 Widget ************** -->

  <Features3
    title="Skills"
    subtitle="Discover the proficiencies that allow me to bring imagination to life through design."
    columns={3}
    defaultIcon="tabler:point-filled"
    items={[
      {
        title: 'Graphic design',
        description: 'Proficient in crafting visually appealing designs that convey messages effectively.',
      },
      {
        title: 'Branding and identity',
        description: 'Skilled at developing cohesive brand identities, including logos and brand guidelines.',
      },
      {
        title: 'User-centered design',
        description: 'Experienced in creating user-friendly interfaces and optimizing user experiences.',
      },
      {
        title: 'Adobe Creative Suite',
        description: 'Skilled in using Photoshop, Illustrator, and InDesign to create and edit visual elements.',
      },
      {
        title: 'Typography',
        description: 'Adept in selecting and manipulating typefaces to enhance design aesthetics.',
      },
      {
        title: 'Color theory',
        description: 'Proficient in using color to evoke emotions and enhance visual harmony.',
      },
      {
        title: 'Print and digital design',
        description: 'Knowledgeable in designing for both print materials and digital platforms.',
      },
      {
        title: 'Attention to detail',
        description: 'Diligent in maintaining precision and quality in all design work.',
      },
      {
        title: 'Adaptability',
        description: 'Quick to adapt to new design trends, technologies, and client preferences.',
      },
    ]}
  />

  <!-- Content Widget **************** -->

  <Content
    id="porfolio"
    title="Elevating visual narratives"
    subtitle="Embark on a design journey that surpasses pixels, entering a realm of imagination. Explore my portfolio, where passion and creativity converge to shape enthralling visual narratives."
    isReversed
    items={[
      {
        title: 'Description:',
        description:
          'Developed a comprehensive brand identity for a tech startup, Tech Innovators, specializing in disruptive innovations. The goal was to convey a modern yet approachable image that resonated with both corporate clients and tech enthusiasts.',
      },
      {
        title: 'Role:',
        description:
          'Led the entire branding process from concept to execution. Created a dynamic logo that symbolized innovation, selected a vibrant color palette, and I designed corporate stationery, website graphics, and social media assets.',
      },
    ]}
    image={{
      src: 'https://images.unsplash.com/photo-1658248165252-71e116af1b34?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=928&q=80',
      alt: 'Tech Design Image',
    }}
    callToAction={{
      target: '_blank',
      text: 'Go to the project',
      icon: 'tabler:chevron-right',
      href: '#',
    }}
  >
    <Fragment slot="content">
      <h3 class="text-2xl font-bold tracking-tight dark:text-white sm:text-3xl mb-2">
        Project 1: <br /><span class="text-2xl">Brand identity for tech innovators</span>
      </h3>
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Content>

  <!-- Content Widget **************** -->

  <Content
    isReversed
    isAfterContent={true}
    items={[
      {
        title: 'Description:',
        description:
          'Designed a captivating event poster for an art and music festival, "ArtWave Fusion," aiming to showcase the synergy between visual art and music genres.',
      },
      {
        title: 'Role:',
        description: `Translated the festival's creative theme into a visually striking poster. Used bold typography, vibrant colors, and abstract elements to depict the fusion of art and music. Ensured the design captured the festival's vibrant atmosphere.`,
      },
    ]}
    image={{
      src: 'https://images.unsplash.com/photo-1619983081563-430f63602796?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80',
      alt: 'Art and Music Poster Image',
    }}
    callToAction={{
      target: '_blank',
      text: 'Go to the project',
      icon: 'tabler:chevron-right',
      href: '#',
    }}
  >
    <Fragment slot="content">
      <h3 class="text-2xl font-bold tracking-tight dark:text-white sm:text-3xl mb-2">
        Project 2: <br /><span class="text-2xl">Event poster for art & music festival</span>
      </h3>
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Content>

  <!-- Content Widget **************** -->

  <Content
    isReversed
    isAfterContent={true}
    items={[
      {
        title: 'Description:',
        description: `Redesigned the e-commerce website for an eco-conscious fashion brand, GreenVogue. The objective was to align the brand's online presence with its sustainable ethos and improve user experience.`,
      },
      {
        title: 'Role:',
        description: `Conducted a thorough analysis of the brand's values and customer base to inform the design direction. Created a visually appealing interface with intuitive navigation, highlighting sustainable materials, and integrating a user-friendly shopping experience.`,
      },
    ]}
    image={{
      src: 'https://plus.unsplash.com/premium_photo-1683288295841-782fa47e4770?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=870&q=80',
      alt: 'Fashion e-commerce Image',
    }}
    callToAction={{
      target: '_blank',
      text: 'Go to the project',
      icon: 'tabler:chevron-right',
      href: '#',
    }}
  >
    <Fragment slot="content">
      <h3 class="text-2xl font-bold tracking-tight dark:text-white sm:text-3xl mb-2">
        Project 3: <br /><span class="text-2xl">E-commerce website redesign for fashion brand</span>
      </h3>
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Content>

  <!-- Testimonials Widget *********** -->

  <Testimonials
    title="Client testimonials"
    subtitle="Discover what clients have to say about their experiences working with me."
    testimonials={[
      {
        testimonial: `She took our vague concept and turned it into a visual masterpiece that perfectly aligned with our goals. Her attention to detail and ability to translate ideas into compelling visuals exceeded our expectations.`,
        name: 'Mark Thompson',
        job: 'Creative director',
        image: {
          src: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80',
          alt: 'Mark Thompson Image',
        },
      },
      {
        testimonial: `She transformed our brand identity with her creative finesse, capturing our essence in every element. Her dedication and talent truly shine through her work.`,
        name: 'Emily Martinez',
        job: 'CEO',
        image: {
          src: 'https://images.unsplash.com/photo-1554151228-14d9def656e4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=772&q=80',
          alt: 'Emily Martinez Image',
        },
      },
      {
        testimonial: `She has an uncanny ability to communicate emotions and stories. She crafted a logo for our NGO that not only represents our cause but also evokes empathy. Her professionalism and commitment make her a designer of exceptional caliber.`,
        name: 'Laura Simmons',
        job: 'Founder of an NGO',
        image: {
          src: 'https://images.unsplash.com/photo-1554727242-741c14fa561c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80',
          alt: 'Laura Simmons Image',
        },
      },
      {
        testimonial: `We entrusted Sarah with revamping our website's user interface, and the results were astounding. Her intuitive design sense enhanced user experience, leading to a significant increase in engagement. She's a designer who truly understands the synergy of aesthetics and functionality.`,
        name: 'Alex Foster',
        job: 'Director of web services',
        image: {
          src: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80',
          alt: 'Alex Foster Image',
        },
      },
      {
        testimonial: `She took our vision and elevated it beyond imagination. Her ability to capture brand essence and translate it into design is nothing short of remarkable. Working with her has been an inspiring journey.`,
        name: 'Jessica Collins',
        job: 'Product Manager',
        image: {
          src: 'https://images.unsplash.com/photo-1548142813-c348350df52b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=778&q=80',
          alt: 'Jessica Collins Image',
        },
      },
      {
        testimonial: `Her ability to transform concepts into captivating visuals is nothing short of extraordinary. She took our event poster idea and turned it into a visual masterpiece that perfectly captured the essence of our festival. Sarah's dedication, creativity, and knack for delivering beyond expectations make her an invaluable asset to any project.`,
        name: 'Michael Carter',
        job: 'Event Coordinator',
        image: {
          src: 'https://images.unsplash.com/photo-1566492031773-4f4e44671857?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80',
          alt: 'Michael Carter Image',
        },
      },
    ]}
  />

  <!-- CallToAction Widget *********** -->

  <CallToAction
    title="Let's create together"
    subtitle="Ready to transform your vision into captivating designs?"
    actions={[
      {
        variant: 'primary',
        text: 'Hire me',
        href: '/',
      },
    ]}
  />

  <!-- BlogLatestPost Widget **************** -->

  <BlogLatestPosts
    id="blog"
    title="Explore my insightful articles on my blog"
    information={`Dive into a realm of design wisdom and creative inspiration, where you'll find invaluable insights, practical tips, and captivating narratives that elevate and enrich your creative journey.`}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </BlogLatestPosts>
</Layout>
