---
import Layout from '~/layouts/PageLayout.astro';
import Image from '~/components/common/Image.astro';

import Hero from '~/components/widgets/Hero.astro';
import Carousel from '~/components/widgets/Carousel.astro';
import Steps from '~/components/widgets/Steps.astro';
import Content from '~/components/widgets/Content.astro';
import FAQs from '~/components/widgets/FAQs.astro';
import Stats from '~/components/widgets/Stats.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';

const metadata = {
  title: 'AstroWind — Free template for creating websites with Astro + Tailwind CSS',
  ignoreTitleTemplate: true,
};
---

<Layout metadata={metadata}>
  <Hero
    class=""
    actions={[
      {
        text: 'Book Your Free Consultation',
        href: 'https://github.com/onwidget/astrowind',
        variant: 'outline',
      },
    ]}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 size-full bg-black opacity-50"></div>
      <Image
        class="size-full z-[-1]"
        objectPosition="center 10%"
        aspectRatio="auto"
        loading="eager"
        src={'~/assets/images/looking_at_ocean.jpg'}
        alt="Hero Image"
      />
    </Fragment>
    <Fragment slot="title">
      Reclaim Your <span class="text-accent font-bold dark:text-white">Youthful</span> Glow
    </Fragment>

    <Fragment slot="subtitle"> Personalised skin treatments for proven, pain-free results. </Fragment>
  </Hero>

  <!-- Content Widget **************** -->

  <Content
    isReversed
    image={{
      src: '~/assets/images/girl_at_resort.jpg',
      alt: 'Girl at Resort',
    }}
  >
    <Fragment slot="content">
      <h3 class="text-2xl sm:text-3xl font-bold tracking-tight dark:text-white mb-4">
        Welcome to <span class="text-secondary">LIFTD</span>
        <span class="text-accent">Skin</span>
      </h3>
      <p>
        Located on the Gold Coast, our clinic offers advanced non-invasive skin treatments that honor your natural
        beauty while delivering the radiant results you deserve.
      </p>
      <h4 class="text-lg sm:text-xl font-bold tracking-tight dark:text-white my-4">Our Approach:</h4>
      <p>
        We know skin isn't one-size-fits-all — everyone has unique concerns, goals, and circumstances, which is why we
        begin with a comprehensive consultation.
      </p>
      <p class="mt-1">
        We then recommend a customized treatment plan based on your specific needs and budget — giving you exactly what
        you need, nothing more, nothing less.
      </p>
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Content>

  <!-- Content Widget **************** -->

  <Content
    isAfterContent
    items={[
      {
        title: 'Fine Lines and Wrinkles',
        description: 'Look refreshed and natural while maintaining your facial expressions and movement.',
      },
      {
        title: 'Acne Scarring and Skin Texture',
        description: 'Improve skin texture and reduce scarring through comfortable, proven technology.',
      },
      {
        title: 'Natural Alternatives to Injectables',
        description:
          "Stimulate your body's natural collagen production for gradual, long-lasting improvements without needles.",
      },

      {
        title: 'Skin Laxity After Weight Loss',
        description: `Address facial and body skin sagging that often follows significant weight loss or procedures like Ozempic.`,
      },
      {
        title: 'Post-Pregnancy Skin Changes',
        description: `Restore your skin's natural elasticity and firmness after childbirth with our gentle, non-invasive treatments.`,
      },
    ]}
    image={{
      src: '~/assets/images/woman_magnifying_glasses.jpg',
      alt: 'Woman Magnifying Glasses',
      height: 500,
      objectPosition: '55% 50%',
    }}
  >
    <Fragment slot="content">
      <h3 class="text-2xl sm:text-3xl font-bold tracking-tight dark:text-white">We Address All Your Skin Concerns</h3>
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Content>

  <!-- Steps Widget ****************** -->

  <Steps
    isReversed
    title="Our Process"
    items={[
      {
        title: 'Step 1: <span class="font-medium">Initial Consultation</span>',
        description:
          'We listen to your skin concerns, aesthetic goals, and lifestyle needs while reviewing your medical history to ensure the safest, most effective approach.',
        icon: 'tabler:package',
      },
      {
        title: 'Step 2: <span class="font-medium">Advanced Skin Analysis</span>',
        description:
          'Our Observ 520X technology reveals hidden skin concerns invisible to the naked eye through advanced multi-spectrum imaging.',
        icon: 'tabler:letter-case',
      },
      {
        title: 'Step 3: <span class="font-medium">Personalized Treatment Plan</span>',
        description:
          'We create your customized HIFU treatment plan with clear pricing, flexible payment options, and recommendations designed to fit your lifestyle and budget.',
        icon: 'tabler:paint',
      },
      {
        title: 'Begin Your Transformation!',
        icon: 'tabler:check',
      },
    ]}
    image={{
      src: '~/assets/images/consultation_smiling.jpeg',
      alt: 'Consultation',
    }}
  />

  <!-- Treatments Widget ************** -->
  <Carousel id="treatments-carousel" showPagination={true} centeredSlides={true} spaceBetween={30} slidesPerView={3} />

  <!-- FAQs Widget ******************* -->

  <FAQs
    title="Frequently Asked Questions"
    subtitle="Dive into the following questions to gain insights into the powerful features that AstroWind offers and how it can elevate your web development journey."
    tagline="FAQs"
    classes={{ container: 'max-w-6xl' }}
    items={[
      {
        title: 'Why AstroWind?',
        description:
          "Michael Knight a young loner on a crusade to champion the cause of the innocent. The helpless. The powerless in a world of criminals who operate above the law. Here he comes Here comes Speed Racer. He's a demon on wheels.",
      },
      {
        title: 'What do I need to start?',
        description:
          'Space, the final frontier. These are the voyages of the Starship Enterprise. Its five-year mission: to explore strange new worlds. Many say exploration is part of our destiny, but it’s actually our duty to future generations.',
      },
      {
        title: 'How to install the Astro + Tailwind CSS template?',
        description:
          "Well, the way they make shows is, they make one show. That show's called a pilot. Then they show that show to the people who make shows, and on the strength of that one show they decide if they're going to make more shows.",
      },
      {
        title: "What's something that you don't understand?",
        description:
          "A flower in my garden, a mystery in my panties. Heart attack never stopped old Big Bear. I didn't even know we were calling him Big Bear.",
      },
      {
        title: 'What is something that you would like to try again?',
        description:
          "A business big enough that it could be listed on the NASDAQ goes belly up. Disappears! It ceases to exist without me. No, you clearly don't know who you're talking to, so let me clue you in.",
      },
      {
        title: 'If you could only ask one question to each person you meet, what would that question be?',
        description:
          "This is not about revenge. This is about justice. A lot of things can change in twelve years, Admiral. Well, that's certainly good to know. About four years. I got tired of hearing how young I looked.",
      },
    ]}
  />

  <!-- Stats Widget ****************** -->

  <Stats
    stats={[
      { title: 'Downloads', amount: '132K' },
      { title: 'Stars', amount: '24.8K' },
      { title: 'Forks', amount: '10.3K' },
      { title: 'Users', amount: '48.4K' },
    ]}
  />

  <!-- CallToAction Widget *********** -->

  <CallToAction
    actions={[
      {
        variant: 'primary',
        text: 'Get template',
        href: 'https://github.com/onwidget/astrowind',
        target: '_blank',
        icon: 'tabler:download',
      },
    ]}
  >
    <Fragment slot="title">
      Astro&nbsp;+&nbsp;<br class="block sm:hidden" /><span class="sm:whitespace-nowrap">Tailwind CSS</span>
    </Fragment>

    <Fragment slot="subtitle">
      Be very surprised by these huge fake numbers you are seeing on this page. <br class="hidden md:inline" />Don't
      waste more time! :P
    </Fragment>
  </CallToAction>
</Layout>
