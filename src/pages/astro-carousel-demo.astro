---
import Layout from '~/layouts/PageLayout.astro';
import Carousel from '~/components/widgets/Carousel.astro';

const metadata = {
  title: 'Astro Carousel Demo',
  description: 'Demonstration of the updated Astro Carousel component with Swiper.js',
};

// Sample slides data for treatments
const treatmentSlides = [
  {
    title: 'HIFU Face Lifting',
    subtitle: 'Non-invasive skin tightening',
    content: 'Advanced ultrasound technology for natural face lifting and skin rejuvenation.',
    image: {
      src: 'https://images.unsplash.com/photo-1616198814651-e71f960c3180?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'HIFU Treatment',
    },
  },
  {
    title: 'Skin Analysis',
    subtitle: 'Advanced diagnostic imaging',
    content: 'Comprehensive skin analysis using Observ 520X technology to reveal hidden concerns.',
    image: {
      src: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Skin Analysis',
    },
  },
  {
    title: 'Personalized Care',
    subtitle: 'Tailored treatment plans',
    content: 'Custom treatment plans designed specifically for your skin type and concerns.',
    image: {
      src: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Personalized Care',
    },
  },
  {
    title: 'Professional Results',
    subtitle: 'Visible improvements',
    content: 'See noticeable improvements in skin texture, firmness, and overall appearance.',
    image: {
      src: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Professional Results',
    },
  },
  {
    title: 'Aftercare Support',
    subtitle: 'Ongoing guidance',
    content: 'Comprehensive aftercare support to maintain and enhance your treatment results.',
    image: {
      src: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Aftercare Support',
    },
  },
];

// Sample testimonials
const testimonialSlides = [
  {
    title: 'Amazing Results!',
    subtitle: '⭐⭐⭐⭐⭐',
    content: '"The HIFU treatment exceeded my expectations. My skin feels tighter and looks more youthful!" - Sarah M.',
  },
  {
    title: 'Professional Service',
    subtitle: '⭐⭐⭐⭐⭐',
    content:
      '"The team was incredibly professional and made me feel comfortable throughout the entire process." - James L.',
  },
  {
    title: 'Visible Improvement',
    subtitle: '⭐⭐⭐⭐⭐',
    content: '"I noticed improvements after just one session. Highly recommend LIFTDSkin!" - Maria K.',
  },
  {
    title: 'Great Experience',
    subtitle: '⭐⭐⭐⭐⭐',
    content: '"From consultation to aftercare, everything was perfect. Will definitely return!" - David R.',
  },
];
---

<Layout metadata={metadata}>
  <main class="py-12 md:py-20">
    <div class="mx-auto max-w-7xl px-4 md:px-6">
      <!-- Page Header -->
      <div class="text-center mb-16">
        <h1 class="text-4xl md:text-6xl font-bold text-primary mb-4">Astro Carousel Demo</h1>
        <p class="text-xl text-muted max-w-3xl mx-auto">
          Experience our updated Astro Carousel component with proper Swiper.js integration
        </p>
      </div>

      <!-- Basic Centered Carousel with Background Images -->
      <section class="mb-20">
        <h2 class="text-3xl font-bold text-center mb-8">Carousel with Background Images</h2>
        <p class="text-center text-muted mb-8 max-w-2xl mx-auto">
          A beautiful centered carousel featuring background images with overlay text. <strong>Test the loop:</strong> Click
          through all pagination dots to verify looping works correctly.
        </p>
        <div class="bg-gray-100 dark:bg-gray-800 p-8 rounded-xl">
          <Carousel
            id="basic-carousel"
            showPagination={true}
            showNavigation={true}
            autoplay={false}
            loop={true}
            centeredSlides={true}
            spaceBetween={30}
            slidesPerView="auto"
            class="mb-8"
          />
        </div>
        <div class="text-center mt-4">
          <p class="text-sm text-muted">
            ✅ Features: Background images, text overlay, loop functionality, and responsive design
          </p>
        </div>
      </section>

      <!-- Treatment Carousel with Images -->
      <section class="mb-20">
        <h2 class="text-3xl font-bold text-center mb-8">Our Treatments</h2>
        <p class="text-center text-muted mb-8 max-w-2xl mx-auto">
          Explore our comprehensive range of skincare treatments with navigation controls.
        </p>
        <Carousel
          id="treatments"
          slides={treatmentSlides}
          showPagination={true}
          showNavigation={true}
          autoplay={false}
          loop={true}
          centeredSlides={true}
          spaceBetween={30}
          slidesPerView="auto"
          class="mb-8"
        />
      </section>

      <!-- Testimonials with Autoplay -->
      <section class="mb-20">
        <h2 class="text-3xl font-bold text-center mb-8">Client Testimonials</h2>
        <p class="text-center text-muted mb-8 max-w-2xl mx-auto">
          Hear what our satisfied clients have to say about their experience.
        </p>
        <Carousel
          id="testimonials"
          slides={testimonialSlides}
          showPagination={true}
          showNavigation={false}
          autoplay={true}
          loop={true}
          centeredSlides={true}
          spaceBetween={20}
          slidesPerView={1}
          class="max-w-4xl mx-auto"
        />
      </section>

      <!-- Features & Usage -->
      <section class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8">
        <h2 class="text-2xl font-bold mb-6">Background Image Features</h2>
        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-lg font-semibold mb-4">Key Features</h3>
            <ul class="space-y-2 text-muted">
              <li>✅ Background images with automatic overlay</li>
              <li>✅ Smart text color adaptation (white on backgrounds)</li>
              <li>✅ Fallback to gradient backgrounds</li>
              <li>✅ Cover positioning for optimal display</li>
              <li>✅ Responsive image loading</li>
              <li>✅ Accessibility with proper alt text</li>
              <li>✅ Smooth hover effects and transitions</li>
              <li>✅ Compatible with existing image prop</li>
            </ul>
          </div>
          <div>
            <h3 class="text-lg font-semibold mb-4">Background Image Usage</h3>
            <pre
              class="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg text-sm overflow-x-auto"><code>{`const slides = [
  {
    title: 'HIFU Treatment',
    subtitle: 'Non-invasive',
    content: 'Advanced technology...',
    backgroundImage: {
      src: 'image-url.jpg',
      alt: 'Background description'
    }
  }
];`}</code></pre>
            <p class="text-xs text-muted mt-2">
              Background images automatically get a dark overlay (bg-black/40) for better text readability.
            </p>
          </div>
        </div>
      </section>
    </div>
  </main>
</Layout>
