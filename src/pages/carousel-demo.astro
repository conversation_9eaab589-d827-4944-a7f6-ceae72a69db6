---
import Layout from '~/layouts/PageLayout.astro';
import Carousel from '~/components/widgets/Carousel.astro';

const metadata = {
  title: 'Carousel Demo',
  description: 'Demonstration of the Carousel component with different configurations',
};

// Sample slides data
const treatmentSlides = [
  {
    title: 'HIFU Face Lifting',
    subtitle: 'Non-invasive skin tightening',
    content: 'Advanced ultrasound technology for natural face lifting and skin rejuvenation.',
    image: {
      src: 'https://images.unsplash.com/photo-1616198814651-e71f960c3180?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'HIFU Treatment'
    }
  },
  {
    title: 'Skin Analysis',
    subtitle: 'Advanced diagnostic imaging',
    content: 'Comprehensive skin analysis using Observ 520X technology to reveal hidden concerns.',
    image: {
      src: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Skin Analysis'
    }
  },
  {
    title: 'Personalized Care',
    subtitle: 'Tailored treatment plans',
    content: 'Custom treatment plans designed specifically for your skin type and concerns.',
    image: {
      src: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Personalized Care'
    }
  },
  {
    title: 'Professional Results',
    subtitle: 'Visible improvements',
    content: 'See noticeable improvements in skin texture, firmness, and overall appearance.',
    image: {
      src: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Professional Results'
    }
  },
  {
    title: 'Aftercare Support',
    subtitle: 'Ongoing guidance',
    content: 'Comprehensive aftercare support to maintain and enhance your treatment results.',
    image: {
      src: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Aftercare Support'
    }
  }
];

const testimonialSlides = [
  {
    title: 'Amazing Results!',
    subtitle: '⭐⭐⭐⭐⭐',
    content: '"The HIFU treatment exceeded my expectations. My skin feels tighter and looks more youthful!" - Sarah M.'
  },
  {
    title: 'Professional Service',
    subtitle: '⭐⭐⭐⭐⭐',
    content: '"The team was incredibly professional and made me feel comfortable throughout the entire process." - James L.'
  },
  {
    title: 'Visible Improvement',
    subtitle: '⭐⭐⭐⭐⭐',
    content: '"I noticed improvements after just one session. Highly recommend LIFTDSkin!" - Maria K.'
  },
  {
    title: 'Great Experience',
    subtitle: '⭐⭐⭐⭐⭐',
    content: '"From consultation to aftercare, everything was perfect. Will definitely return!" - David R.'
  }
];
---

<Layout metadata={metadata}>
  <main class="py-12 md:py-20">
    <div class="mx-auto max-w-7xl px-4 md:px-6">
      
      <!-- Page Header -->
      <div class="text-center mb-16">
        <h1 class="text-4xl md:text-6xl font-bold text-primary mb-4">
          Carousel Component Demo
        </h1>
        <p class="text-xl text-muted max-w-3xl mx-auto">
          Explore different configurations of our flexible Carousel component built with Swiper.js
        </p>
      </div>

      <!-- Treatment Carousel with Navigation -->
      <section class="mb-20">
        <h2 class="text-3xl font-bold text-center mb-8">Our Treatments</h2>
        <Carousel
          id="treatments"
          slides={treatmentSlides}
          showPagination={true}
          showNavigation={true}
          autoplay={false}
          loop={true}
          centeredSlides={true}
          spaceBetween={30}
          slidesPerView="auto"
          class="mb-8"
        />
      </section>

      <!-- Testimonials Carousel with Autoplay -->
      <section class="mb-20">
        <h2 class="text-3xl font-bold text-center mb-8">What Our Clients Say</h2>
        <Carousel
          id="testimonials"
          slides={testimonialSlides}
          showPagination={true}
          showNavigation={false}
          autoplay={true}
          loop={true}
          centeredSlides={false}
          spaceBetween={20}
          slidesPerView={1}
          class="max-w-4xl mx-auto"
        />
      </section>

      <!-- Simple Carousel -->
      <section class="mb-20">
        <h2 class="text-3xl font-bold text-center mb-8">Simple Carousel</h2>
        <Carousel
          id="simple"
          showPagination={true}
          showNavigation={false}
          autoplay={false}
          loop={true}
          centeredSlides={true}
          spaceBetween={20}
          slidesPerView="auto"
          class="max-w-5xl mx-auto"
        />
      </section>

      <!-- Usage Instructions -->
      <section class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8">
        <h2 class="text-2xl font-bold mb-6">How to Use the Carousel Component</h2>
        <div class="prose dark:prose-invert max-w-none">
          <h3>Basic Usage</h3>
          <pre class="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg overflow-x-auto"><code>&lt;Carousel
  id="my-carousel"
  slides={mySlides}
  showPagination={true}
  showNavigation={false}
  autoplay={false}
  loop={true}
/&gt;</code></pre>
          
          <h3>Props</h3>
          <ul>
            <li><strong>id</strong>: Unique identifier for the carousel</li>
            <li><strong>slides</strong>: Array of slide objects with title, subtitle, content, and optional image</li>
            <li><strong>showPagination</strong>: Show/hide pagination dots</li>
            <li><strong>showNavigation</strong>: Show/hide navigation arrows</li>
            <li><strong>autoplay</strong>: Enable/disable autoplay</li>
            <li><strong>loop</strong>: Enable/disable infinite loop</li>
            <li><strong>centeredSlides</strong>: Center the active slide</li>
            <li><strong>spaceBetween</strong>: Space between slides in pixels</li>
            <li><strong>slidesPerView</strong>: Number of slides per view or 'auto'</li>
          </ul>
        </div>
      </section>

    </div>
  </main>
</Layout>
